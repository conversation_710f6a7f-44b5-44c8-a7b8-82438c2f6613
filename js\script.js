class RubiksCube {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cube = null;
        this.pieces = [];
        this.isRotating = false;
        this.mouse = { x: 0, y: 0 };
        this.mouseDown = false;
        this.raycaster = new THREE.Raycaster();
        this.mouseVector = new THREE.Vector2();
        this.solveSequence = [];
        this.solvingInProgress = false;

        this.colors = {
            front: 0xff0000,   // Red
            back: 0xff8c00,    // Orange
            right: 0x0000ff,   // Blue
            left: 0x00ff00,    // Green
            top: 0xffff00,     // Yellow
            bottom: 0xffffff   // White
        };

        this.init();
        this.createCube();
        this.setupControls();
        this.animate();
    }

    init() {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a2e);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(6, 6, 6);
        this.camera.lookAt(0, 0, 0);

        // Renderer setup
        const canvas = document.getElementById('game-canvas');
        this.renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight2.position.set(-10, -10, -5);
        this.scene.add(directionalLight2);

        // Cube container
        this.cube = new THREE.Group();
        this.scene.add(this.cube);
    }

    createCube() {
        const size = 0.9;
        const gap = 0.1;

        // Create 27 small cubes (3x3x3)
        for (let x = -1; x <= 1; x++) {
            for (let y = -1; y <= 1; y++) {
                for (let z = -1; z <= 1; z++) {
                    const piece = this.createPiece(x, y, z, size);
                    piece.position.set(x * (size + gap), y * (size + gap), z * (size + gap));
                    this.cube.add(piece);
                    this.pieces.push(piece);
                }
            }
        }

        // Set initial rotation for better viewing
        this.cube.rotation.set(0.3, 0.5, 0);
    }

    createPiece(x, y, z, size) {
        const geometry = new THREE.BoxGeometry(size, size, size);
        const materials = [];

        // Create materials for each face
        // Right face (x = 1)
        materials.push(new THREE.MeshLambertMaterial({
            color: x === 1 ? this.colors.right : 0x000000
        }));
        // Left face (x = -1)
        materials.push(new THREE.MeshLambertMaterial({
            color: x === -1 ? this.colors.left : 0x000000
        }));
        // Top face (y = 1)
        materials.push(new THREE.MeshLambertMaterial({
            color: y === 1 ? this.colors.top : 0x000000
        }));
        // Bottom face (y = -1)
        materials.push(new THREE.MeshLambertMaterial({
            color: y === -1 ? this.colors.bottom : 0x000000
        }));
        // Front face (z = 1)
        materials.push(new THREE.MeshLambertMaterial({
            color: z === 1 ? this.colors.front : 0x000000
        }));
        // Back face (z = -1)
        materials.push(new THREE.MeshLambertMaterial({
            color: z === -1 ? this.colors.back : 0x000000
        }));

        const piece = new THREE.Mesh(geometry, materials);
        piece.castShadow = true;
        piece.receiveShadow = true;

        // Store original position for reference
        piece.userData = { originalPosition: { x, y, z } };

        return piece;
    }

    setupControls() {
        const canvas = this.renderer.domElement;

        // Mouse controls for cube rotation
        canvas.addEventListener('mousedown', (event) => {
            this.mouseDown = true;
            this.mouse.x = event.clientX;
            this.mouse.y = event.clientY;
        });

        canvas.addEventListener('mousemove', (event) => {
            if (!this.mouseDown || this.isRotating) return;

            const deltaX = event.clientX - this.mouse.x;
            const deltaY = event.clientY - this.mouse.y;

            this.cube.rotation.y += deltaX * 0.01;
            this.cube.rotation.x += deltaY * 0.01;

            this.mouse.x = event.clientX;
            this.mouse.y = event.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            this.mouseDown = false;
        });

        // Face rotation buttons
        document.querySelectorAll('.face-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const face = btn.dataset.face;
                this.rotateFace(face);
            });
        });

        // Button controls
        document.getElementById('scramble').addEventListener('click', () => {
            this.scramble();
        });

        document.getElementById('solve').addEventListener('click', () => {
            this.autoSolve();
        });

        document.getElementById('reset').addEventListener('click', () => {
            this.reset();
        });

        // Guide modal controls
        const modal = document.getElementById('guide-modal');
        const guideBtn = document.getElementById('guide-toggle');
        const closeBtn = document.querySelector('.close');

        guideBtn.addEventListener('click', () => {
            modal.style.display = 'block';
        });

        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });

        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }

    scramble() {
        if (this.isRotating || this.solvingInProgress) return;

        const faces = ['F', 'B', 'R', 'L', 'U', 'D'];
        const moves = [];

        // Generate 20 random moves
        for (let i = 0; i < 20; i++) {
            const face = faces[Math.floor(Math.random() * faces.length)];
            moves.push(face);
        }

        this.executeMovesSequence(moves, 150);
    }

    rotateFace(face) {
        if (this.isRotating || this.solvingInProgress) return;

        switch(face) {
            case 'F': // Front
                this.rotateLayer('z', 1, Math.PI / 2);
                break;
            case 'B': // Back
                this.rotateLayer('z', -1, -Math.PI / 2);
                break;
            case 'R': // Right
                this.rotateLayer('x', 1, Math.PI / 2);
                break;
            case 'L': // Left
                this.rotateLayer('x', -1, -Math.PI / 2);
                break;
            case 'U': // Up
                this.rotateLayer('y', 1, Math.PI / 2);
                break;
            case 'D': // Down
                this.rotateLayer('y', -1, -Math.PI / 2);
                break;
        }
    }

    autoSolve() {
        if (this.isRotating || this.solvingInProgress) return;

        // Simple solve sequence - just demonstrate the concept
        const solveSequence = [
            'F', 'R', 'U', 'R\'', 'U\'', 'F\'',  // Basic algorithm
            'R', 'U', 'R\'', 'U', 'R', 'U2', 'R\'',  // Another algorithm
            'U', 'R', 'U\'', 'L\'', 'U', 'R\'', 'U\'', 'L'  // Corner algorithm
        ];

        this.solvingInProgress = true;
        this.executeMovesSequence(solveSequence, 500, () => {
            this.solvingInProgress = false;
            // After demo sequence, reset to solved state
            setTimeout(() => this.reset(), 1000);
        });
    }

    executeMovesSequence(moves, delay, callback) {
        let index = 0;

        const executeNext = () => {
            if (index >= moves.length) {
                if (callback) callback();
                return;
            }

            const move = moves[index];
            this.rotateFace(move);
            index++;

            setTimeout(executeNext, delay);
        };

        executeNext();
    }

    reset() {
        if (this.isRotating) return;

        this.solvingInProgress = false;

        // Reset all pieces to their original positions and rotations
        this.pieces.forEach((piece, index) => {
            const x = Math.floor(index / 9) - 1;
            const y = Math.floor((index % 9) / 3) - 1;
            const z = (index % 3) - 1;

            piece.position.set(x * 1, y * 1, z * 1);
            piece.rotation.set(0, 0, 0);
        });

        // Reset cube rotation to a nice viewing angle
        this.cube.rotation.set(0.3, 0.5, 0);
    }

    rotateLayer(axis, layer, angle) {
        if (this.isRotating) return;
        this.isRotating = true;

        // Get pieces in the specified layer
        const layerPieces = this.pieces.filter(piece => {
            const pos = piece.userData.originalPosition;
            if (axis === 'x') return Math.abs(pos.x - layer) < 0.1;
            if (axis === 'y') return Math.abs(pos.y - layer) < 0.1;
            if (axis === 'z') return Math.abs(pos.z - layer) < 0.1;
            return false;
        });

        // Create a temporary group for rotation
        const rotationGroup = new THREE.Group();
        this.cube.add(rotationGroup);

        // Store original positions relative to cube
        const originalPositions = [];
        layerPieces.forEach(piece => {
            originalPositions.push(piece.position.clone());
            this.cube.remove(piece);
            rotationGroup.add(piece);
        });

        // Animate rotation
        const startRotation = 0;
        const endRotation = angle;
        const duration = 400; // milliseconds
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Smooth easing
            const easeProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);
            const currentRotation = startRotation + (endRotation - startRotation) * easeProgress;

            rotationGroup.rotation[axis] = currentRotation;

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Move pieces back to main cube and update their positions
                layerPieces.forEach((piece) => {
                    rotationGroup.remove(piece);
                    this.cube.add(piece);

                    // Update the piece's userData position
                    const newPos = piece.position.clone();
                    piece.userData.originalPosition = {
                        x: Math.round(newPos.x),
                        y: Math.round(newPos.y),
                        z: Math.round(newPos.z)
                    };
                });

                this.cube.remove(rotationGroup);
                this.isRotating = false;
            }
        };

        animate();
    }

    onWindowResize() {
        const canvas = this.renderer.domElement;
        const container = canvas.parentElement;

        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the game when the page loads
window.addEventListener('DOMContentLoaded', () => {
    new RubiksCube();
});