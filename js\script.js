class RubiksCube {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cube = null;
        this.pieces = [];
        this.isRotating = false;
        this.mouse = { x: 0, y: 0 };
        this.mouseDown = false;

        this.colors = {
            front: 0xff0000,   // Red
            back: 0xff8c00,    // Orange
            right: 0x0000ff,   // Blue
            left: 0x00ff00,    // Green
            top: 0xffff00,     // Yellow
            bottom: 0xffffff   // White
        };

        this.init();
        this.createCube();
        this.setupControls();
        this.animate();
    }

    init() {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(5, 5, 5);
        this.camera.lookAt(0, 0, 0);

        // Renderer setup
        const canvas = document.getElementById('game-canvas');
        this.renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        // Cube container
        this.cube = new THREE.Group();
        this.scene.add(this.cube);
    }

    createCube() {
        const size = 0.9;
        const gap = 0.1;

        // Create 27 small cubes (3x3x3)
        for (let x = -1; x <= 1; x++) {
            for (let y = -1; y <= 1; y++) {
                for (let z = -1; z <= 1; z++) {
                    const piece = this.createPiece(x, y, z, size);
                    piece.position.set(x * (size + gap), y * (size + gap), z * (size + gap));
                    this.cube.add(piece);
                    this.pieces.push(piece);
                }
            }
        }
    }

    createPiece(x, y, z, size) {
        const geometry = new THREE.BoxGeometry(size, size, size);
        const materials = [];

        // Create materials for each face
        // Right face (x = 1)
        materials.push(new THREE.MeshLambertMaterial({
            color: x === 1 ? this.colors.right : 0x000000
        }));
        // Left face (x = -1)
        materials.push(new THREE.MeshLambertMaterial({
            color: x === -1 ? this.colors.left : 0x000000
        }));
        // Top face (y = 1)
        materials.push(new THREE.MeshLambertMaterial({
            color: y === 1 ? this.colors.top : 0x000000
        }));
        // Bottom face (y = -1)
        materials.push(new THREE.MeshLambertMaterial({
            color: y === -1 ? this.colors.bottom : 0x000000
        }));
        // Front face (z = 1)
        materials.push(new THREE.MeshLambertMaterial({
            color: z === 1 ? this.colors.front : 0x000000
        }));
        // Back face (z = -1)
        materials.push(new THREE.MeshLambertMaterial({
            color: z === -1 ? this.colors.back : 0x000000
        }));

        const piece = new THREE.Mesh(geometry, materials);
        piece.castShadow = true;
        piece.receiveShadow = true;

        // Store original position for reference
        piece.userData = { originalPosition: { x, y, z } };

        return piece;
    }

    setupControls() {
        const canvas = this.renderer.domElement;

        // Mouse controls for cube rotation
        canvas.addEventListener('mousedown', (event) => {
            this.mouseDown = true;
            this.mouse.x = event.clientX;
            this.mouse.y = event.clientY;
        });

        canvas.addEventListener('mousemove', (event) => {
            if (!this.mouseDown || this.isRotating) return;

            const deltaX = event.clientX - this.mouse.x;
            const deltaY = event.clientY - this.mouse.y;

            this.cube.rotation.y += deltaX * 0.01;
            this.cube.rotation.x += deltaY * 0.01;

            this.mouse.x = event.clientX;
            this.mouse.y = event.clientY;
        });

        canvas.addEventListener('mouseup', () => {
            this.mouseDown = false;
        });

        // Button controls
        document.getElementById('scramble').addEventListener('click', () => {
            this.scramble();
        });

        document.getElementById('solve').addEventListener('click', () => {
            this.solve();
        });

        document.getElementById('reset').addEventListener('click', () => {
            this.reset();
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
    }

    scramble() {
        if (this.isRotating) return;

        const rotations = ['x', 'y', 'z'];
        const directions = [1, -1];
        const layers = [-1, 0, 1];

        // Perform 20 random rotations
        for (let i = 0; i < 20; i++) {
            setTimeout(() => {
                const axis = rotations[Math.floor(Math.random() * rotations.length)];
                const direction = directions[Math.floor(Math.random() * directions.length)];
                const layer = layers[Math.floor(Math.random() * layers.length)];

                this.rotateLayer(axis, layer, direction * Math.PI / 2);
            }, i * 100);
        }
    }

    solve() {
        if (this.isRotating) return;
        // Simple solve - just reset to original state
        this.reset();
    }

    reset() {
        if (this.isRotating) return;

        // Reset all pieces to their original positions and rotations
        this.pieces.forEach((piece, index) => {
            const x = Math.floor(index / 9) - 1;
            const y = Math.floor((index % 9) / 3) - 1;
            const z = (index % 3) - 1;

            piece.position.set(x * 1, y * 1, z * 1);
            piece.rotation.set(0, 0, 0);
        });

        // Reset cube rotation
        this.cube.rotation.set(0, 0, 0);
    }

    rotateLayer(axis, layer, angle) {
        if (this.isRotating) return;
        this.isRotating = true;

        // Get pieces in the specified layer
        const layerPieces = this.pieces.filter(piece => {
            const pos = piece.userData.originalPosition;
            return pos[axis] === layer;
        });

        // Create a temporary group for rotation
        const rotationGroup = new THREE.Group();
        this.scene.add(rotationGroup);

        // Move pieces to rotation group
        layerPieces.forEach(piece => {
            const worldPosition = new THREE.Vector3();
            piece.getWorldPosition(worldPosition);
            this.cube.remove(piece);
            rotationGroup.add(piece);
            piece.position.copy(worldPosition);
        });

        // Animate rotation
        const startRotation = rotationGroup.rotation[axis];
        const endRotation = startRotation + angle;
        const duration = 300; // milliseconds
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Smooth easing
            const easeProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);

            rotationGroup.rotation[axis] = startRotation + (endRotation - startRotation) * easeProgress;

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Move pieces back to main cube
                layerPieces.forEach(piece => {
                    const worldPosition = new THREE.Vector3();
                    piece.getWorldPosition(worldPosition);
                    rotationGroup.remove(piece);
                    this.cube.add(piece);
                    piece.position.copy(worldPosition);
                });

                this.scene.remove(rotationGroup);
                this.isRotating = false;
            }
        };

        animate();
    }

    onWindowResize() {
        const canvas = this.renderer.domElement;
        const container = canvas.parentElement;

        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the game when the page loads
window.addEventListener('DOMContentLoaded', () => {
    new RubiksCube();
});