class cube {
    constructor(game) {
        this.geometry = {
        pieceCornerRadius: 0.12;
        edgeCornerRoundness: 0.15;
        edgeScale: 0.82;
        edgeDepth: 0.81;
    };

    this.holder = new THREE.Object3D();
    this.object = new THREE.Object3D();
    this.animator = new THREE.Object3D();

    this.placeholder.add(this.animator);
    this.animator.add(this.object);

    this.game.world.scence.add(this.holder);
}

}