<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rubik's Cube Game</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="container">
        <div id="ui">
            <h1>Rubik's Cube</h1>
            <div id="controls">
                <button id="scramble">Scramble</button>
                <button id="solve">Auto Solve</button>
                <button id="reset">Reset</button>
                <button id="guide-toggle">Show Guide</button>
            </div>
            <div id="instructions">
                <p><strong>Controls:</strong></p>
                <p>• Drag to rotate cube</p>
                <p>• Click faces to rotate layers</p>
                <p>• Use buttons for actions</p>
            </div>
            <div id="face-controls">
                <h3>Face Rotations:</h3>
                <div class="face-buttons">
                    <button class="face-btn" data-face="F">F (Front)</button>
                    <button class="face-btn" data-face="B">B (Back)</button>
                    <button class="face-btn" data-face="R">R (Right)</button>
                    <button class="face-btn" data-face="L">L (Left)</button>
                    <button class="face-btn" data-face="U">U (Up)</button>
                    <button class="face-btn" data-face="D">D (Down)</button>
                </div>
            </div>
        </div>
        <div id="game-container">
            <canvas id="game-canvas"></canvas>
        </div>
    </div>

    <!-- Guide Modal -->
    <div id="guide-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Rubik's Cube Solving Guide</h2>
            <div class="guide-content">
                <h3>Step 1: White Cross</h3>
                <p>Form a white cross on the top face, ensuring edge pieces match center colors.</p>

                <h3>Step 2: White Corners</h3>
                <p>Complete the white face by positioning white corners correctly.</p>

                <h3>Step 3: Middle Layer</h3>
                <p>Position the middle layer edge pieces using right-hand and left-hand algorithms.</p>

                <h3>Step 4: Yellow Cross</h3>
                <p>Create a yellow cross on the top face using the algorithm: F R U R' U' F'</p>

                <h3>Step 5: Yellow Face</h3>
                <p>Complete the yellow face using: R U R' U R U2 R'</p>

                <h3>Step 6: Position Corners</h3>
                <p>Position yellow corners correctly using: U R U' L' U R' U' L</p>

                <h3>Step 7: Final Layer</h3>
                <p>Orient the last layer edges: R U R' F' R U R' U' R' F R2 U' R'</p>

                <h3>Notation:</h3>
                <p><strong>F</strong> = Front clockwise, <strong>R</strong> = Right clockwise</p>
                <p><strong>U</strong> = Up clockwise, <strong>L</strong> = Left clockwise</p>
                <p><strong>'</strong> = Counter-clockwise, <strong>2</strong> = 180° turn</p>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>