* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    height: 100vh;
    overflow: hidden;
}

#container {
    display: flex;
    height: 100vh;
}

#ui {
    width: 280px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    color: white;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
}

#ui h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

#controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

#controls button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

#controls button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#instructions {
    font-size: 14px;
    line-height: 1.6;
    opacity: 0.8;
}

#instructions p {
    margin-bottom: 10px;
}

#game-container {
    flex: 1;
    position: relative;
}

#game-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab;
}

#game-canvas:active {
    cursor: grabbing;
}

#face-controls {
    margin-top: 20px;
}

#face-controls h3 {
    margin-bottom: 15px;
    font-size: 16px;
}

.face-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.face-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.face-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: white;
}

.guide-content h3 {
    color: #ffd700;
    margin-top: 20px;
    margin-bottom: 10px;
}

.guide-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}