* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

#container {
    display: flex;
    height: 100vh;
}

#ui {
    width: 250px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    color: white;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

#ui h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

#controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

#controls button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

#controls button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#instructions {
    font-size: 14px;
    line-height: 1.6;
    opacity: 0.8;
}

#instructions p {
    margin-bottom: 10px;
}

#game-container {
    flex: 1;
    position: relative;
}

#game-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab;
}

#game-canvas:active {
    cursor: grabbing;
}